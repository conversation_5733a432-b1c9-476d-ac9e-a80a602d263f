@extends('Layouts.app')

@section('title', 'RX-Info | Drugs')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Drugs Management<span class="page-desc text-muted fs-7 fw-semibold pt-1">Manage and organize drug information</span>
                                </h1>
                            </div>
                            <div><a href="{{ route('drugs.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Add New Drug</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="table-responsive">
                                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="drugs-table">
                                        <thead>
                                            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                                <th class="min-w-50px">#</th>
                                                <th class="min-w-150px">Generic Name</th>
                                                <th class="min-w-150px">Brand Name</th>
                                                <th class="min-w-150px">IUPAC Name</th>
                                                <th class="min-w-150px">Created By</th>
                                                <th class="min-w-125px">Created At</th>
                                                <th class="text-end min-w-100px">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-gray-600 fw-semibold">
                                            <!-- Data will be loaded via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Define columns for drugs table
            const columns = [
                getColumnDefinition('index'),
                { data: 'generic_name', name: 'generic_name' },
                { data: 'brand_name', name: 'brand_name' },
                { data: 'iupac_name', name: 'iupac_name' },
                getColumnDefinition('date', { data: 'created_by', name: 'created_by' }),
                getColumnDefinition('date', { data: 'created_at', name: 'created_at' }),
                getColumnDefinition('actions')
            ];

            // Initialize DataTable using helper function
            let table = initializeDataTable({
                tableId: 'drugs-table',
                ajaxUrl: '{{ route("drugs.index") }}',
                columns: columns,
                itemName: 'drug',
                order: [[5, 'desc']], // Order by created_at desc
                language: {
                    emptyTable: '<div class="text-center py-4"><i class="fas fa-pills fa-3x text-muted mb-3"></i><br><span class="text-muted">No drugs found</span></div>'
                }
            });

            // Delete functionality using universal function
            $(document).on('click', '.delete-drug', function() {
                const drugId = $(this).data('id');
                universalDelete({id: drugId, url: '{{ route("drugs.destroy", ":id") }}', itemName: 'drug', table: table});
            });

            // Refresh button functionality using helper function
            $(document).on('click', '.refresh-table', function() {
                refreshTable(table, 'Drugs table has been refreshed.');
            });
        });
    </script>
@endsection