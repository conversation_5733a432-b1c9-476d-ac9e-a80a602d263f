<?php $__env->startSection('title', 'RX-Info | Drugs'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                    Drugs Management
                                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">
                                        Manage and organize drug information
                                    </span>
                                </h1>
                            </div>
                            <div>
                                <a href="<?php echo e(route('drugs.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add New Drug
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="table-responsive">
                                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="drugs-table">
                                        <thead>
                                            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                                <th class="min-w-50px">#</th>
                                                <th class="min-w-150px">Generic Name</th>
                                                <th class="min-w-150px">Brand Name</th>
                                                <th class="min-w-150px">IUPAC Name</th>

                                                <th class="min-w-150px">Created By</th>
                                                <th class="min-w-125px">Created At</th>
                                                <th class="text-end min-w-100px">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-gray-600 fw-semibold">
                                            <!-- Data will be loaded via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            let table = $('#drugs-table').DataTable({
                processing: true,
                serverSide: true,
                responsive: true,
                ajax: {
                    url: '<?php echo e(route("drugs.index")); ?>',
                    type: 'GET',
                    data: function(d) {
                        // No additional filters
                    },
                    error: function(xhr, error, thrown) {
                        console.error('DataTable AJAX Error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error Loading Data',
                            text: 'Failed to load drugs data. Please refresh the page.',
                            confirmButtonText: 'Refresh Page'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                location.reload();
                            }
                        });
                    }
                },
                columns: [
                    {
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    },
                    {
                        data: 'generic_name',
                        name: 'generic_name'
                    },
                    {
                        data: 'brand_name',
                        name: 'brand_name'
                    },
                    {
                        data: 'iupac_name',
                        name: 'iupac_name'
                    },

                    {
                        data: 'created_by',
                        name: 'created_by',
                        className: 'text-center'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at',
                        className: 'text-center'
                    },
                    {
                        data: 'actions',
                        name: 'actions',
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    }
                ],
                order: [[5, 'desc']], // Order by created_at desc
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                language: {
                    processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>',
                    emptyTable: '<div class="text-center py-4"><i class="fas fa-pills fa-3x text-muted mb-3"></i><br><span class="text-muted">No drugs found</span></div>',
                    zeroRecords: '<div class="text-center py-4"><i class="fas fa-search fa-3x text-muted mb-3"></i><br><span class="text-muted">No matching records found</span></div>',
                    lengthMenu: "Show _MENU_ entries",
                    search: "Search:",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    },
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 to 0 of 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)"
                },
                drawCallback: function(settings) {
                    // Reinitialize tooltips after each draw
                    $('[title]').tooltip();

                    // Add loading state management
                    $('#drugs-table').removeClass('table-loading');
                }
            });



            // Delete functionality
            $(document).on('click', '.delete-drug', function() {
                let drugId = $(this).data('id');

                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this action!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Show loading
                        Swal.fire({
                            title: 'Deleting...',
                            text: 'Please wait while we delete the drug.',
                            allowOutsideClick: false,
                            allowEscapeKey: false,
                            showConfirmButton: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Perform delete request
                        $.ajax({
                            url: '<?php echo e(route("drugs.destroy", ":id")); ?>'.replace(':id', drugId),
                            type: 'DELETE',
                            success: function(response) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Deleted!',
                                    text: response.message || 'Drug has been deleted successfully.',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                                table.ajax.reload(null, false); // Reload table without resetting pagination
                            },
                            error: function(xhr) {
                                let errorMessage = 'An error occurred while deleting the drug.';
                                if (xhr.responseJSON && xhr.responseJSON.message) {
                                    errorMessage = xhr.responseJSON.message;
                                }

                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error!',
                                    text: errorMessage
                                });
                            }
                        });
                    }
                });
            });

            // Add loading state when table is processing
            table.on('processing.dt', function(e, settings, processing) {
                if (processing) {
                    $('#drugs-table').addClass('table-loading');
                } else {
                    $('#drugs-table').removeClass('table-loading');
                }
            });

            // Initialize tooltips
            $('[title]').tooltip();

            // Refresh button functionality (if you want to add one)
            $(document).on('click', '.refresh-table', function() {
                table.ajax.reload(null, false);
                Swal.fire({
                    icon: 'success',
                    title: 'Refreshed!',
                    text: 'Table data has been refreshed.',
                    timer: 1500,
                    showConfirmButton: false
                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\rx_info\resources\views/web/drugs/index.blade.php ENDPATH**/ ?>