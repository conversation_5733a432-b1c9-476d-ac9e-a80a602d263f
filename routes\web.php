<?php

use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Web\DrugsController;
use Illuminate\Support\Facades\Route;

// Login Routes
Route::get('/', [AuthController::class, 'showLoginForm'])->name('showloginform');
Route::post('/login', [AuthController::class, 'login'])->name('login');

Route::group(['middleware' => ['admin.auth']], function () {
    // Dashboard and Logout Routes
    Route::get('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/admin-dashboard', [AuthController::class, 'showDashboard'])->name('admin-dashboard');

    // Drugs Routes
    Route::resource('drugs', DrugsController::class);
});
