<div id="kt_app_footer" class="app-footer">
    <div class="app-container container-fluid d-flex flex-column flex-md-row flex-center flex-md-stack py-3">
        <div class="text-dark order-2 order-md-1">
            <span class="text-muted fw-semibold me-1">{{ date('Y') }}&copy;</span>
            <a href="#" target="_blank" class="text-gray-800 text-hover-primary">RX-Info</a>
            <span class="text-muted fw-semibold me-1"> - A full-service healthcare provider</span>
        </div>
        <ul class="menu menu-gray-600 menu-hover-primary fw-semibold order-1">
            <li class="menu-item">
                <a href="#" target="_blank" class="menu-link px-2">About</a>
            </li>
            <li class="menu-item">
                <a href="#" target="_blank" class="menu-link px-2">Support</a>
            </li>
            <li class="menu-item">
                <a href="#" target="_blank" class="menu-link px-2">Contact</a>
            </li>
        </ul>
    </div>
</div>

{{-- ================================ SCRIPTS SECTION ================================ --}}

{{-- Core JavaScript Libraries --}}
<script src="{{ asset('plugins/global/plugins.bundle.js') }}"></script>
<script src="{{ asset('js/scripts.bundle.js') }}"></script>

{{-- jQuery (if not already included in plugins.bundle.js) --}}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

{{-- SweetAlert2 for Notifications --}}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

{{-- DataTables Core --}}
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

{{-- DataTables Extensions --}}
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>

{{-- Export Libraries for DataTables --}}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

{{-- Font Awesome (if needed for icons) --}}
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>

{{-- Bootstrap 5 (if not already included) --}}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

{{-- Global AJAX Setup --}}
<script>
    $(document).ready(function() {
        // Global CSRF Token Setup for all AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Global AJAX Error Handler
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (xhr.status === 419) {
                // CSRF token mismatch
                Swal.fire({
                    icon: 'error',
                    title: 'Session Expired',
                    text: 'Your session has expired. Please refresh the page.',
                    confirmButtonText: 'Refresh Page'
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload();
                    }
                });
            } else if (xhr.status === 401) {
                // Unauthorized
                Swal.fire({
                    icon: 'error',
                    title: 'Unauthorized',
                    text: 'You are not authorized to perform this action.',
                    confirmButtonText: 'OK'
                });
            } else if (xhr.status === 500) {
                // Server error
                Swal.fire({
                    icon: 'error',
                    title: 'Server Error',
                    text: 'An internal server error occurred. Please try again later.',
                    confirmButtonText: 'OK'
                });
            }
        });

        // Global loading state for AJAX requests
        $(document).ajaxStart(function() {
            // You can add a global loading indicator here if needed
        }).ajaxStop(function() {
            // Hide global loading indicator here if needed
        });

        // Initialize all tooltips globally
        $('[data-bs-toggle="tooltip"]').tooltip();
        $('[title]').tooltip();

        // Initialize all popovers globally
        $('[data-bs-toggle="popover"]').popover();
    });
</script>

{{-- Page-specific scripts --}}
@yield('scripts')