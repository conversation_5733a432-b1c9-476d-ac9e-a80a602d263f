

<?php $__env->startSection('title', 'RX-Info | Edit Drug'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Edit Drug<span class="page-desc text-muted fs-7 fw-semibold pt-1">Update drug information</span></h1>
                            </div>
                            <div>
                                <a href="<?php echo e(route('drugs.index')); ?>" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Drugs</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <form id="drug-edit-form" action="<?php echo e(route('drugs.update', $drug->id)); ?>" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <!-- Basic Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-info-circle text-primary me-2"></i>Basic Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Generic Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="generic_name" class="form-label fw-semibold fs-6 required">Generic Name</label>
                                                <input type="text" name="generic_name" id="generic_name" class="form-control form-control-solid <?php $__errorArgs = ['generic_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter generic name" value="<?php echo e($drug->generic_name); ?>" required>
                                                <?php $__errorArgs = ['generic_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Scientific/generic name of the drug</div>
                                            </div>
                                        </div>

                                        <!-- Brand Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="brand_name" class="form-label fw-semibold fs-6 required">Brand Name</label>
                                                <input type="text" name="brand_name" id="brand_name" class="form-control form-control-solid <?php $__errorArgs = ['brand_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter brand name" value="<?php echo e($drug->brand_name); ?>" required>
                                                <?php $__errorArgs = ['brand_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Commercial/brand name of the drug</div>
                                            </div>
                                        </div>

                                        <!-- IUPAC Name -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="iupac_name" class="form-label fw-semibold fs-6">IUPAC Name</label>
                                                <input type="text" name="iupac_name" id="iupac_name" class="form-control form-control-solid <?php $__errorArgs = ['iupac_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter IUPAC name" value="<?php echo e($drug->iupac_name); ?>">
                                                <?php $__errorArgs = ['iupac_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">International Union of Pure and Applied Chemistry (IUPAC) name of the drug</div>
                                            </div>
                                        </div>

                                        <!-- Drug Image -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_image" class="form-label fw-semibold fs-6">Drug Image</label>
                                                <input type="file" name="drug_image" id="drug_image" class="form-control form-control-solid <?php $__errorArgs = ['drug_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" accept="image/*">
                                                <?php $__errorArgs = ['drug_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Upload drug image (JPG, PNG, GIF - Max: 2MB)</div>
                                                <div id="image-preview" class="mt-3" style="display: none;">
                                                    <img id="preview-img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Detailed Information Card -->
                            <div class="card card-flush mb-6">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3 class="fw-bold text-dark"><i class="fas fa-file-medical text-success me-2"></i>Detailed Information</h3>
                                    </div>
                                </div>
                                <div class="card-body pt-6">
                                    <div class="row g-6">
                                        <!-- Drug Description -->
                                        <div class="col-12">
                                            <div class="fv-row">
                                                <label for="drug_description" class="form-label fw-semibold fs-6 required">Drug Description</label>
                                                <textarea name="drug_description" id="drug_description" class="form-control form-control-solid <?php $__errorArgs = ['drug_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="4" placeholder="Enter detailed description of the drug..." required><?php echo e($drug->drug_description); ?></textarea>
                                                <?php $__errorArgs = ['drug_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Comprehensive description of the drug and its properties</div>
                                            </div>
                                        </div>

                                        <!-- Drug Indication -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_indication" class="form-label fw-semibold fs-6 required">Drug Indication</label>
                                                <textarea name="drug_indication" id="drug_indication" class="form-control form-control-solid <?php $__errorArgs = ['drug_indication'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="4" placeholder="Enter medical conditions this drug treats..." required><?php echo e($drug->drug_indication); ?></textarea>
                                                <?php $__errorArgs = ['drug_indication'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Medical conditions and symptoms this drug is used to treat</div>
                                            </div>
                                        </div>

                                        <!-- Drug Dosage -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_dosage" class="form-label fw-semibold fs-6 required">Drug Dosage</label>
                                                <textarea name="drug_dosage" id="drug_dosage" class="form-control form-control-solid <?php $__errorArgs = ['drug_dosage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="4" placeholder="Enter dosage information..." required><?php echo e($drug->drug_dosage); ?></textarea>
                                                <?php $__errorArgs = ['drug_dosage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Recommended dosage amounts and instructions</div>
                                            </div>
                                        </div>

                                        <!-- Route of Administration -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_route_of_administration" class="form-label fw-semibold fs-6 required">Route of Administration</label>
                                                <textarea name="drug_route_of_administration" id="drug_route_of_administration" class="form-control form-control-solid <?php $__errorArgs = ['drug_route_of_administration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="3" placeholder="Enter administration routes (oral, injection, etc.)..." required><?php echo e($drug->drug_route_of_administration); ?></textarea>
                                                <?php $__errorArgs = ['drug_route_of_administration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">How the drug should be administered (oral, IV, IM, etc.)</div>
                                            </div>
                                        </div>

                                        <!-- Drug Frequency -->
                                        <div class="col-md-6">
                                            <div class="fv-row">
                                                <label for="drug_frequency" class="form-label fw-semibold fs-6 required">Drug Frequency</label>
                                                <textarea name="drug_frequency" id="drug_frequency" class="form-control form-control-solid <?php $__errorArgs = ['drug_frequency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="3" placeholder="Enter frequency (once daily, twice daily, etc.)..." required><?php echo e($drug->drug_frequency); ?></textarea>
                                                <?php $__errorArgs = ['drug_frequency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">How often the drug should be taken</div>
                                            </div>
                                        </div>

                                        <!-- Drug Precautions -->
                                        <div class="col-12">
                                            <div class="fv-row">
                                                <label for="drug_precautions" class="form-label fw-semibold fs-6 required">Drug Precautions</label>
                                                <textarea name="drug_precautions" id="drug_precautions" class="form-control form-control-solid <?php $__errorArgs = ['drug_precautions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="4" placeholder="Enter precautions, warnings contraindications..." required><?php echo e($drug->drug_precautions); ?></textarea>
                                                <?php $__errorArgs = ['drug_precautions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <div class="form-text">Important precautions, warnings, side effects, and contraindications</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Form Actions -->
                            <div class="card card-flush">
                                <div class="card-body text-center py-8">
                                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submit-btn">
                                        <span class="indicator-label"><i class="fas fa-save me-2"></i>Update Drug</span>
                                        <span class="indicator-progress" style="display: none;">Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <a href="<?php echo e(route('drugs.index')); ?>" class="btn btn-secondary btn-lg"><i class="fas fa-times me-2"></i>Cancel</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with all functionalities
            initializeEnhancedForm({
                formId: 'drug-edit-form',
                submitBtnId: 'submit-btn',
                imageInputId: 'drug_image',
                imagePreviewId: 'image-preview',
                previewImageId: 'preview-img',
                exitSelector: 'a[href="<?php echo e(route("drugs.index")); ?>"]',
                successMessage: 'Drug has been updated successfully.',
                redirectUrl: '<?php echo e(route("drugs.index")); ?>',
                hasFileUpload: true
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\rx_info\resources\views/web/drugs/edit.blade.php ENDPATH**/ ?>